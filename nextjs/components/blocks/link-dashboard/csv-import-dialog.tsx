"use client";

import { useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Upload,
  Download,
  CheckCircle,
  AlertCircle,
  Loader2,
  AlertTriangle,
  ExternalLink,
  Search
} from "lucide-react";
import { toast } from "sonner";
import { checkDuplicate, batchValidateUrls } from "@/utils/csv-import-validation";

interface CsvImportDialogProps {
  projectId: string;
  onImportComplete: () => void;
}

type ImportFormat = "sermush-links" | "ahrefs" | "custom";

type ValidationStatus = 'pending' | 'valid' | 'invalid' | 'checking';
type DuplicateStatus = 'none' | 'exact' | 'similar' | 'checking';

interface ParsedDiscoveredLink {
  id: string; // Temporary ID for UI tracking
  url: string;
  title: string;
  source_url: string;
  source_title: string;
  anchor_text: string;
  link_type: string;
  dr_score?: number;
  traffic?: number;
  is_nofollow: boolean;
  is_sponsored: boolean;
  first_seen?: Date;
  last_seen?: Date;
  // Enhanced fields for validation and duplicate detection
  validation: {
    status: ValidationStatus;
    isAccessible?: boolean;
    statusCode?: number;
    error?: string;
  };
  duplicate: {
    status: DuplicateStatus;
    existingLink?: any;
    similarity?: number;
  };
  selected: boolean;
  rowIndex: number;
}

interface ParseProgress {
  stage: 'reading' | 'parsing' | 'validating' | 'checking-duplicates' | 'ready' | 'uploading' | 'complete';
  current: number;
  total: number;
  validLinks: ParsedDiscoveredLink[];
  errors: string[];
  warnings: string[];
}

interface ImportSummary {
  totalParsed: number;
  validLinks: number;
  invalidLinks: number;
  duplicates: number;
  selected: number;
  toImport: number;
}

export function CsvImportDialog({ projectId, onImportComplete }: CsvImportDialogProps) {
  const [open, setOpen] = useState(false);
  const [format, setFormat] = useState<ImportFormat>("sermush-links");
  const [file, setFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [progress, setProgress] = useState<ParseProgress | null>(null);

  // Enhanced workflow state
  const [currentStep, setCurrentStep] = useState<'upload' | 'preview' | 'confirm'>('upload');
  const [parsedLinks, setParsedLinks] = useState<ParsedDiscoveredLink[]>([]);
  const [existingLinks, setExistingLinks] = useState<any[]>([]);
  const [filterText, setFilterText] = useState('');
  const [showOnlySelected, setShowOnlySelected] = useState(false);
  const [showOnlyDuplicates, setShowOnlyDuplicates] = useState(false);
  const [duplicateAction, setDuplicateAction] = useState<'skip' | 'update' | 'import'>('skip');

  const formatOptions = [
    { value: "sermush-links", label: "sermush Links Export" },
    // { value: "ahrefs", label: "Ahrefs Backlinks Export" },
    { value: "custom", label: "Custom CSV Format" },
  ];

  const formatDescriptions = {
    "sermush-links": "CSV format from Semrush Links with columns: Page ascore, Source title, Source url, Target url, Anchor, etc. This will import backlinks and update domain statistics in the all_links table.",
    "ahrefs": "CSV export from Ahrefs backlinks report with standard Ahrefs columns. These will be imported as discovered backlinks to your project.",
    "custom": "Custom CSV format - use the template below for proper formatting. These will be imported as discovered backlinks to your project."
  };

  // Helper functions for the enhanced workflow
  const fetchExistingLinks = useCallback(async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}/discovered-links`);
      if (response.ok) {
        const data = await response.json();
        setExistingLinks(data.links || []);
      }
    } catch (error) {
      console.error('Failed to fetch existing links:', error);
    }
  }, [projectId]);

  const toggleLinkSelection = useCallback((linkId: string) => {
    setParsedLinks(prev => prev.map(link =>
      link.id === linkId ? { ...link, selected: !link.selected } : link
    ));
  }, []);

  const toggleAllSelection = useCallback((selected: boolean) => {
    setParsedLinks(prev => prev.map(link => ({ ...link, selected })));
  }, []);

  const getFilteredLinks = useCallback(() => {
    return parsedLinks.filter(link => {
      if (showOnlySelected && !link.selected) return false;
      if (showOnlyDuplicates && link.duplicate.status === 'none') return false;
      if (filterText) {
        const searchText = filterText.toLowerCase();
        return link.url.toLowerCase().includes(searchText) ||
               link.title.toLowerCase().includes(searchText) ||
               link.anchor_text.toLowerCase().includes(searchText);
      }
      return true;
    });
  }, [parsedLinks, showOnlySelected, showOnlyDuplicates, filterText]);

  const getImportSummary = useCallback((): ImportSummary => {
    const selected = parsedLinks.filter(link => link.selected);
    const duplicates = parsedLinks.filter(link => link.duplicate.status !== 'none');
    const invalid = parsedLinks.filter(link => link.validation.status === 'invalid');

    return {
      totalParsed: parsedLinks.length,
      validLinks: parsedLinks.filter(link => link.validation.status === 'valid').length,
      invalidLinks: invalid.length,
      duplicates: duplicates.length,
      selected: selected.length,
      toImport: selected.filter(link =>
        link.validation.status === 'valid' &&
        (link.duplicate.status === 'none' || duplicateAction !== 'skip')
      ).length
    };
  }, [parsedLinks, duplicateAction]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile && selectedFile.type === "text/csv") {
      setFile(selectedFile);
    } else {
      toast.error("Please select a valid CSV file");
    }
  };

  const parseCSVRow = (row: string): string[] => {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < row.length; i++) {
      const char = row[i];
      
      if (char === '"' && (i === 0 || row[i-1] === ',')) {
        inQuotes = true;
      } else if (char === '"' && inQuotes && (i === row.length - 1 || row[i+1] === ',')) {
        inQuotes = false;
      } else if (char === ',' && !inQuotes) {
        result.push(current);
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current);
    return result;
  };

  const parseDate = (dateString: string): Date | null => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  };

  const parseLinkByFormat = (rowData: Record<string, string>, format: string, rowIndex: number): ParsedDiscoveredLink | null => {
    switch (format) {
      case 'sermush-links':
        return parsesermushLinksFormat(rowData, rowIndex);
      case 'ahrefs':
        return parseAhrefsFormat(rowData, rowIndex);
      case 'custom':
        return parseCustomFormat(rowData, rowIndex);
      default:
        return null;
    }
  };

  const parsesermushLinksFormat = (data: Record<string, string>, rowIndex: number): ParsedDiscoveredLink | null => {
    const sourceUrl = data['Source url'];
    const targetUrl = data['Target url'];

    if (!sourceUrl || !targetUrl) {
      return null;
    }

    return {
      id: `temp-${rowIndex}-${Date.now()}`,
      url: sourceUrl, // 外部网站URL (发现的外链来源)
      title: data['Source title'] || '',
      source_url: targetUrl, // 项目网站URL (被链接的目标)
      source_title: data['Source title'] || '',
      anchor_text: data['Anchor'] || '',
      link_type: 'backlink',
      dr_score: parseInt(data['Page ascore']) || undefined,
      traffic: parseInt(data['External links']) || undefined,
      is_nofollow: data['Nofollow']?.toLowerCase() === 'true',
      is_sponsored: data['Sponsored']?.toLowerCase() === 'true',
      first_seen: parseDate(data['First seen']),
      last_seen: parseDate(data['Last seen']),
      validation: {
        status: 'pending' as ValidationStatus
      },
      duplicate: {
        status: 'none' as DuplicateStatus
      },
      selected: true,
      rowIndex
    };
  };

  const parseAhrefsFormat = (data: Record<string, string>, rowIndex: number): ParsedDiscoveredLink | null => {
    const sourceUrl = data['URL'];
    const targetUrl = data['Target URL'];

    if (!sourceUrl || !targetUrl) {
      return null;
    }

    return {
      id: `temp-${rowIndex}-${Date.now()}`,
      url: targetUrl, // The target URL is what we're tracking backlinks to
      title: data['Title'] || '',
      source_url: sourceUrl, // The source URL is where the backlink comes from
      source_title: data['Title'] || '',
      anchor_text: data['Anchor text'] || '',
      link_type: 'backlink',
      dr_score: parseInt(data['DR']) || undefined,
      traffic: parseInt(data['Traffic']) || undefined,
      is_nofollow: data['Nofollow']?.toLowerCase() === 'true',
      is_sponsored: data['Sponsored']?.toLowerCase() === 'true',
      first_seen: parseDate(data['First seen']),
      last_seen: parseDate(data['Last seen']),
      validation: {
        status: 'pending' as ValidationStatus
      },
      duplicate: {
        status: 'none' as DuplicateStatus
      },
      selected: true,
      rowIndex
    };
  };

  const parseCustomFormat = (data: Record<string, string>, rowIndex: number): ParsedDiscoveredLink | null => {
    const sourceUrl = data['source_url'];
    const targetUrl = data['target_url'];

    if (!sourceUrl || !targetUrl) {
      return null;
    }

    return {
      id: `temp-${rowIndex}-${Date.now()}`,
      url: targetUrl, // The target URL is what we're tracking backlinks to
      title: data['source_title'] || '',
      source_url: sourceUrl, // The source URL is where the backlink comes from
      source_title: data['source_title'] || '',
      anchor_text: data['anchor_text'] || '',
      link_type: 'backlink',
      dr_score: parseInt(data['dr_score']) || undefined,
      traffic: parseInt(data['traffic']) || undefined,
      is_nofollow: data['is_nofollow']?.toLowerCase() === 'true',
      is_sponsored: data['is_sponsored']?.toLowerCase() === 'true',
      first_seen: parseDate(data['first_seen']),
      last_seen: parseDate(data['last_seen']),
      validation: {
        status: 'pending' as ValidationStatus
      },
      duplicate: {
        status: 'none' as DuplicateStatus
      },
      selected: true,
      rowIndex
    };
  };

  const handleParseCSV = async () => {
    if (!file) {
      toast.error("Please select a CSV file");
      return;
    }

    setImporting(true);
    setProgress({ stage: 'reading', current: 0, total: 0, validLinks: [], errors: [], warnings: [] });

    try {
      // Stage 1: Read and parse CSV
      const csvContent = await file.text();
      const lines = csvContent.trim().split('\n');

      if (lines.length < 2) {
        toast.error("CSV file must contain at least a header and one data row");
        setImporting(false);
        return;
      }

      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
      const dataRows = lines.slice(1);

      setProgress(prev => ({ ...prev!, stage: 'parsing', total: dataRows.length }));

      // Stage 2: Parse rows
      const validLinks: ParsedDiscoveredLink[] = [];
      const errors: string[] = [];

      for (let i = 0; i < dataRows.length; i++) {
        try {
          const row = parseCSVRow(dataRows[i]);
          if (row.length < headers.length) {
            errors.push(`Row ${i + 2}: Incomplete data (${row.length}/${headers.length} columns)`);
            continue;
          }

          const rowData = Object.fromEntries(
            headers.map((header, index) => [header, row[index]?.trim().replace(/"/g, '') || ''])
          );

          const parsedLink = parseLinkByFormat(rowData, format, i);
          if (parsedLink) {
            validLinks.push(parsedLink);
          } else {
            errors.push(`Row ${i + 2}: Invalid or missing required fields`);
          }
        } catch (error) {
          errors.push(`Row ${i + 2}: ${error}`);
        }

        // Update progress every 10 rows
        if (i % 10 === 0 || i === dataRows.length - 1) {
          setProgress(prev => ({ ...prev!, current: i + 1, validLinks: [...validLinks], errors: [...errors] }));
          // Allow UI to update
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      if (validLinks.length === 0) {
        toast.error("No valid links found in CSV file");
        setImporting(false);
        return;
      }

      // Stage 3: Fetch existing links for duplicate checking
      await fetchExistingLinks();

      // Stage 4: Validate URLs
      setProgress(prev => ({ ...prev!, stage: 'validating', current: 0, total: validLinks.length }));

      const urlsToValidate = validLinks.map(link => link.url);
      const validationResults = await batchValidateUrls(urlsToValidate, (completed, total) => {
        setProgress(prev => ({ ...prev!, current: completed }));
      });

      // Update links with validation results
      const linksWithValidation = validLinks.map(link => ({
        ...link,
        validation: {
          status: validationResults.get(link.url)?.isValid ?
            (validationResults.get(link.url)?.isAccessible ? 'valid' : 'invalid') as ValidationStatus :
            'invalid' as ValidationStatus,
          isAccessible: validationResults.get(link.url)?.isAccessible,
          statusCode: validationResults.get(link.url)?.statusCode,
          error: validationResults.get(link.url)?.error
        }
      }));

      // Stage 5: Check for duplicates
      setProgress(prev => ({ ...prev!, stage: 'checking-duplicates', current: 0, total: linksWithValidation.length }));

      const linksWithDuplicates = await Promise.all(
        linksWithValidation.map(async (link, index) => {
          const duplicateResult = await checkDuplicate(link.url, projectId, existingLinks);
          setProgress(prev => ({ ...prev!, current: index + 1 }));

          return {
            ...link,
            duplicate: {
              status: duplicateResult.isDuplicate ?
                (duplicateResult.type === 'exact' ? 'exact' : 'similar') as DuplicateStatus :
                'none' as DuplicateStatus,
              existingLink: duplicateResult.existingLink,
              similarity: duplicateResult.similarity
            }
          };
        })
      );

      // Stage 6: Ready for preview
      setParsedLinks(linksWithDuplicates);
      setProgress(prev => ({ ...prev!, stage: 'ready' }));
      setCurrentStep('preview');

      toast.success(`Parsed ${linksWithDuplicates.length} links. Review and select links to import.`);
    } catch (error) {
      console.error("Error parsing CSV:", error);
      toast.error("Failed to parse CSV file");
    } finally {
      setImporting(false);
    }
  };

  const handleFinalImport = async () => {
    const selectedLinks = parsedLinks.filter(link => link.selected);

    if (selectedLinks.length === 0) {
      toast.error("Please select at least one link to import");
      return;
    }

    setImporting(true);
    setProgress({ stage: 'uploading', current: 0, total: selectedLinks.length, validLinks: [], errors: [], warnings: [] });

    try {
      // Filter out duplicates based on user preference
      const linksToImport = selectedLinks.filter(link => {
        if (link.duplicate.status !== 'none' && duplicateAction === 'skip') {
          return false;
        }
        return link.validation.status === 'valid';
      });

      if (linksToImport.length === 0) {
        toast.error("No valid links to import after filtering");
        setImporting(false);
        return;
      }

      // 根据格式选择不同的 API 端点
      const apiEndpoint = format === 'sermush-links'
        ? "/api/discovered-links/semrush-import"
        : "/api/discovered-links/batch-import";

      let requestBody: any;

      if (format === 'sermush-links') {
        // Semrush 格式需要特殊处理
        requestBody = {
          projectId,
          links: linksToImport.map(link => ({
            page_ascore: link.dr_score || 0,
            source_title: link.title || '',
            source_url: link.url, // 外部网站URL
            target_url: link.source_url, // 项目网站URL
            anchor: link.anchor_text || '',
            external_links: link.traffic || 0,
            internal_links: 0,
            nofollow: link.is_nofollow || false,
            sponsored: link.is_sponsored || false,
            ugc: false,
            text: true,
            frame: false,
            form: false,
            image: false,
            sitewide: false,
            first_seen: link.first_seen?.toISOString() || new Date().toISOString(),
            last_seen: link.last_seen?.toISOString() || new Date().toISOString(),
            new_link: false,
            lost_link: false
          }))
        };
      } else {
        // 其他格式使用原有的数据结构
        requestBody = {
          projectId,
          links: linksToImport.map(link => ({
            url: link.url,
            title: link.title,
            source_url: link.source_url,
            source_title: link.source_title,
            anchor_text: link.anchor_text,
            dr_score: link.dr_score,
            traffic: link.traffic,
            is_nofollow: link.is_nofollow,
            is_sponsored: link.is_sponsored,
            first_seen: link.first_seen?.toISOString(),
            last_seen: link.last_seen?.toISOString()
          }))
        };
      }

      const response = await fetch(apiEndpoint, {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const result = await response.json();

      if (response.ok) {
        setProgress(prev => ({ ...prev!, stage: 'complete' }));

        // 根据格式显示不同的成功消息
        if (format === 'sermush-links') {
          toast.success(`Semrush import completed! Processed ${linksToImport.length} backlinks. ${result.newLinks || 0} new backlinks imported.`);
          if (result.domainStatsUpdated) {
            toast.success(`Updated statistics for ${result.domainStatsUpdated} domains in all_links table.`);
          }
        } else {
          toast.success(`Import completed! Processed ${linksToImport.length} backlinks. ${result.newLinks || 0} new backlinks imported.`);
        }

        if (result.errors && result.errors.length > 0) {
          console.log('Import errors:', result.errors);
          toast.warning(`${result.errors.length} backlinks had errors and were skipped.`);
        }

        if (result.domainStatsErrors && result.domainStatsErrors.length > 0) {
          console.log('Domain stats errors:', result.domainStatsErrors);
          toast.warning(`${result.domainStatsErrors.length} domain statistics updates failed.`);
        }

        if (result.debug) {
          console.log('Import debug info:', result.debug);
        }

        // Reset state and close dialog
        setOpen(false);
        setFile(null);
        setProgress(null);
        setParsedLinks([]);
        setCurrentStep('upload');
        onImportComplete();
      } else {
        console.error('Import failed:', result);
        toast.error(result.error || "Failed to import CSV");
      }
    } catch (error) {
      console.error("Error importing CSV:", error);
      toast.error("Failed to import CSV");
    } finally {
      setImporting(false);
    }
  };

  const downloadTemplate = () => {
    const templates = {
      "sermush-links": `Page ascore,Source title,Source url,Target url,Anchor,External links,Internal links,Nofollow,Sponsored,Ugc,Text,Frame,Form,Image,Sitewide,First seen,Last seen,New link,Lost link
8,JitHub程序员 - 即刻App,https://m.okjike.com/topics/55e02198dcef9f0e00d7b3c3,https://card.hekmon.com/,card.hekmon.com,6,10,FALSE,FALSE,FALSE,TRUE,FALSE,FALSE,FALSE,FALSE,2025-05-05,2025-06-04,FALSE,TRUE
7,不务正业小胡同学的个人主页 - 即刻App,http://m.okjike.com/users/4E822130-4B7B-4DB9-A4CA-B326397ADB32,https://card.hekmon.com/,card.hekmon.com,14,22,FALSE,FALSE,FALSE,TRUE,FALSE,FALSE,FALSE,FALSE,2025-05-01,2025-06-04,FALSE,TRUE`,
      "ahrefs": `Domain,URL,Title,Target URL,Anchor text,DR,UR,Traffic,First seen,Last seen,Nofollow,Sponsored
example.com,https://example.com/page,Example Page Title,https://yourdomain.com,your domain,60,45,1000,2025-04-13,2025-05-04,FALSE,FALSE`,
      "custom": `source_url,source_title,target_url,anchor_text,dr_score,traffic,first_seen,last_seen,is_nofollow,is_sponsored
https://example.com,Example Source Site,https://yourdomain.com,your domain name,60,1000,2025-04-13,2025-05-04,false,false`
    };

    const content = templates[format];
    const blob = new Blob([content], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${format}-template.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Component for rendering individual link preview
  const LinkPreviewItem = ({ link }: { link: ParsedDiscoveredLink }) => {
    const getValidationIcon = () => {
      switch (link.validation.status) {
        case 'valid':
          return <CheckCircle className="h-4 w-4 text-green-500" />;
        case 'invalid':
          return <AlertCircle className="h-4 w-4 text-red-500" />;
        case 'checking':
          return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
        default:
          return <AlertTriangle className="h-4 w-4 text-gray-400" />;
      }
    };

    const getDuplicateIcon = () => {
      switch (link.duplicate.status) {
        case 'exact':
          return <Badge variant="destructive" className="text-xs">Exact Duplicate</Badge>;
        case 'similar':
          return <Badge variant="outline" className="text-xs">Similar ({Math.round((link.duplicate.similarity || 0) * 100)}%)</Badge>;
        default:
          return null;
      }
    };

    return (
      <Card className="mb-2">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <Checkbox
              checked={link.selected}
              onCheckedChange={() => toggleLinkSelection(link.id)}
              className="mt-1"
            />
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-2">
                {getValidationIcon()}
                <span className="font-medium text-sm truncate">{link.title || 'Untitled'}</span>
                {getDuplicateIcon()}
              </div>

              <div className="space-y-1 text-xs text-gray-600">
                <div className="flex items-center space-x-1">
                  <ExternalLink className="h-3 w-3" />
                  <span className="truncate">{link.url}</span>
                </div>
                {link.anchor_text && (
                  <div>
                    <span className="font-medium">Anchor:</span> {link.anchor_text}
                  </div>
                )}
                {link.dr_score && (
                  <div>
                    <span className="font-medium">DR:</span> {link.dr_score}
                  </div>
                )}
                {link.validation.error && (
                  <div className="text-red-500">
                    <span className="font-medium">Error:</span> {link.validation.error}
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Upload className="h-4 w-4 mr-2" />
          Import Backlinks
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Import Discovered Backlinks from CSV</DialogTitle>
          <DialogDescription>
            {currentStep === 'upload' && "Upload and parse your CSV file to import backlinks"}
            {currentStep === 'preview' && "Review and select links to import"}
            {currentStep === 'confirm' && "Confirm your import settings"}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={currentStep} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload" disabled={importing}>
              1. Upload & Parse
            </TabsTrigger>
            <TabsTrigger value="preview" disabled={parsedLinks.length === 0}>
              2. Review & Select
            </TabsTrigger>
            <TabsTrigger value="confirm" disabled={parsedLinks.filter(l => l.selected).length === 0}>
              3. Import
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4">
            {!importing ? (
              <>
                <div className="space-y-2">
                  <Label htmlFor="format">Import Format</Label>
                  <Select value={format} onValueChange={(value: ImportFormat) => setFormat(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select import format" />
                    </SelectTrigger>
                    <SelectContent>
                      {formatOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    {formatDescriptions[format]}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="file">CSV File</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".csv"
                    onChange={handleFileChange}
                    className="cursor-pointer"
                  />
                  {file && (
                    <p className="text-sm text-green-600">
                      Selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
                    </p>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadTemplate}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Download Template
                  </Button>

                  <div className="flex gap-2">
                    <Button variant="outline" onClick={() => setOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleParseCSV} disabled={!file || importing}>
                      {importing ? "Parsing..." : "Parse CSV"}
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <div className="space-y-4">
                {/* Progress Display */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    {progress?.stage === 'ready' ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                    )}
                    <span className="font-medium">
                      {progress?.stage === 'reading' && 'Reading CSV file...'}
                      {progress?.stage === 'parsing' && 'Parsing CSV data...'}
                      {progress?.stage === 'validating' && 'Validating URLs...'}
                      {progress?.stage === 'checking-duplicates' && 'Checking for duplicates...'}
                      {progress?.stage === 'ready' && 'Ready for review!'}
                    </span>
                  </div>

                  {progress && progress.total > 0 && progress.stage !== 'ready' && (
                    <div className="space-y-2">
                      <Progress
                        value={(progress.current / progress.total) * 100}
                        className="w-full"
                      />
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>
                          {progress.stage === 'parsing' && `Processed ${progress.current} of ${progress.total} rows`}
                          {progress.stage === 'validating' && `Validated ${progress.current} of ${progress.total} URLs`}
                          {progress.stage === 'checking-duplicates' && `Checked ${progress.current} of ${progress.total} links`}
                        </span>
                        <span>{Math.round((progress.current / progress.total) * 100)}%</span>
                      </div>
                    </div>
                  )}

                  {progress && progress.errors.length > 0 && (
                    <div className="text-sm space-y-1">
                      <div className="flex items-center gap-2 text-amber-600">
                        <AlertCircle className="h-4 w-4" />
                        <span>{progress.errors.length} parsing errors</span>
                      </div>
                    </div>
                  )}
                </div>

                {progress?.stage === 'ready' && (
                  <div className="flex justify-end">
                    <Button onClick={() => setCurrentStep('preview')}>
                      Review Links
                    </Button>
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <div className="space-y-4">
              {/* Summary Stats */}
              <div className="grid grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold">{parsedLinks.length}</div>
                    <div className="text-sm text-muted-foreground">Total Parsed</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {parsedLinks.filter(l => l.validation.status === 'valid').length}
                    </div>
                    <div className="text-sm text-muted-foreground">Valid URLs</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-amber-600">
                      {parsedLinks.filter(l => l.duplicate.status !== 'none').length}
                    </div>
                    <div className="text-sm text-muted-foreground">Duplicates</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {parsedLinks.filter(l => l.selected).length}
                    </div>
                    <div className="text-sm text-muted-foreground">Selected</div>
                  </CardContent>
                </Card>
              </div>

              {/* Filters and Controls */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4" />
                    <Input
                      placeholder="Filter links..."
                      value={filterText}
                      onChange={(e) => setFilterText(e.target.value)}
                      className="w-64"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={showOnlySelected}
                      onCheckedChange={(checked) => setShowOnlySelected(checked === true)}
                    />
                    <Label>Show only selected</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={showOnlyDuplicates}
                      onCheckedChange={(checked) => setShowOnlyDuplicates(checked === true)}
                    />
                    <Label>Show only duplicates</Label>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleAllSelection(true)}
                  >
                    Select All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleAllSelection(false)}
                  >
                    Deselect All
                  </Button>
                </div>
              </div>

              {/* Links List */}
              <ScrollArea className="h-96 border rounded-md p-4">
                {getFilteredLinks().map((link) => (
                  <LinkPreviewItem key={link.id} link={link} />
                ))}
                {getFilteredLinks().length === 0 && (
                  <div className="text-center text-muted-foreground py-8">
                    No links match the current filters
                  </div>
                )}
              </ScrollArea>

              {/* Navigation */}
              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setCurrentStep('upload')}>
                  Back to Upload
                </Button>
                <Button
                  onClick={() => setCurrentStep('confirm')}
                  disabled={parsedLinks.filter(l => l.selected).length === 0}
                >
                  Continue to Import ({parsedLinks.filter(l => l.selected).length} selected)
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="confirm" className="space-y-4">
            <div className="space-y-4">
              {/* Import Summary */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-4">Import Summary</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-muted-foreground">Selected Links</div>
                      <div className="text-2xl font-bold">{getImportSummary().selected}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Will Import</div>
                      <div className="text-2xl font-bold text-green-600">{getImportSummary().toImport}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Duplicates Found</div>
                      <div className="text-2xl font-bold text-amber-600">{getImportSummary().duplicates}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Invalid URLs</div>
                      <div className="text-2xl font-bold text-red-600">{getImportSummary().invalidLinks}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Duplicate Handling */}
              {getImportSummary().duplicates > 0 && (
                <Card>
                  <CardContent className="p-6">
                    <h3 className="font-semibold mb-4">Duplicate Handling</h3>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="skip"
                          name="duplicateAction"
                          value="skip"
                          checked={duplicateAction === 'skip'}
                          onChange={(e) => setDuplicateAction(e.target.value as 'skip' | 'update' | 'import')}
                        />
                        <Label htmlFor="skip">Skip duplicates (recommended)</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="update"
                          name="duplicateAction"
                          value="update"
                          checked={duplicateAction === 'update'}
                          onChange={(e) => setDuplicateAction(e.target.value as 'skip' | 'update' | 'import')}
                        />
                        <Label htmlFor="update">Update existing links</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="import"
                          name="duplicateAction"
                          value="import"
                          checked={duplicateAction === 'import'}
                          onChange={(e) => setDuplicateAction(e.target.value as 'skip' | 'update' | 'import')}
                        />
                        <Label htmlFor="import">Import as new links anyway</Label>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Final Import */}
              {!importing ? (
                <div className="flex justify-between">
                  <Button variant="outline" onClick={() => setCurrentStep('preview')}>
                    Back to Review
                  </Button>
                  <Button onClick={handleFinalImport} className="bg-green-600 hover:bg-green-700">
                    Import {getImportSummary().toImport} Links
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                    <span className="font-medium">Importing links...</span>
                  </div>
                  {progress && progress.total > 0 && (
                    <Progress value={(progress.current / progress.total) * 100} className="w-full" />
                  )}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}